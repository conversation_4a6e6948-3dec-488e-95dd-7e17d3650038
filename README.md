# Clip: Modern Windows Clipboard Manager

Clip is a fast, modern, and lightweight clipboard manager for Windows, built with Electron, React, and TypeScript. It stores your clipboard history (text and images), features a beautiful UI, and is easy to install with no admin rights required.

## Features
- Clipboard history for text and images (default: 100 items)
- Modern UI: rounded corners, smooth animations, dark/light mode
- Keyboard shortcut to open (Ctrl+Shift+V), Esc to close
- Settings for max items and auto-delete policy
- Local storage (no cloud by default)
- Extremely fast to launch

## Getting Started
1. Install dependencies:
   ```sh
   npm install
   ```
2. Start the app:
   ```sh
   npm run start
   ```

## Roadmap
- [x] Local clipboard history
- [x] Modern UI
- [ ] Cloud sync (future)

## License
MIT
