{"name": "clip", "description": "A fast, modern clipboard manager for Windows.", "version": "1.0.0", "author": "<PERSON><PERSON><PERSON>", "copyright": "© 2025 <PERSON><PERSON><PERSON>", "main": "dist/main/main.js", "scripts": {"build:main": "tsc --project src/tsconfig.main.json && node -e \"require('fs').copyFileSync('src/preload.js','dist/main/preload.js')\" && node scripts/copy-clipmsg.js", "build:renderer": "webpack --mode production", "build:all": "npm run build:main && npm run build:renderer", "dist": "npm run build:all && electron-builder --config.win.target=zip", "dist:installer": "npm run build:all && electron-builder --config.win.target=nsis", "dist:portable": "npm run build:all && electron-builder --config.win.target=zip", "dist:both": "npm run build:all && electron-builder", "dist:fast": "npm run build:all && electron-builder --config.compression=store", "dist:fast-installer": "npm run build:all && electron-builder --config.compression=store --config.win.target=nsis", "dist:fast-portable": "npm run build:all && electron-builder --config.compression=store --config.win.target=zip", "build:dir": "npm run build:all && electron-builder --dir", "test:build": "npm run build:all && electron-builder --dir --config.compression=store", "start": "npm run build:all && echo \"starting electron...\" && electron .", "dev": "npm run build:main && webpack --mode development --watch & electron .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "license": "MIT", "type": "commonjs", "dependencies": {"better-sqlite3": "^11.10.0", "fuse.js": "^7.1.0", "node-addon-api": "^8.3.1", "node-key-sender": "^1.0.11", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "electron": "25.9.0", "html-webpack-plugin": "^5.6.3", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.99.8", "webpack-cli": "^6.0.1", "electron-builder": "^26.0.12"}, "build": {"appId": "com.sukarth.clip", "productName": "Clip", "copyright": "© 2025 <PERSON><PERSON><PERSON>", "icon": "assets/icon.ico", "files": ["dist/**/*", "src/preload.js", "native/SendPaste.exe", "native/AutoHotkey.exe", "native/clipmsg.node", "!**/.git", "!**/node_modules/*/{README.md,readme.md,CHANGELOG.md,changelog.md}", "!**/node_modules/*/{test,tests,spec,specs}", "!**/node_modules/**/*.d.ts", "!**/*.{o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraFiles": [{"from": "assets/README-portable.txt", "to": "README.txt", "filter": ["**/*"]}], "directories": {"buildResources": ".", "output": "dist"}, "fileAssociations": [], "compression": "normal", "buildDependenciesFromSource": false, "nodeGypRebuild": false, "buildVersion": "1.0.0", "win": {"icon": "assets/icon.ico", "executableName": "Clip", "target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "signAndEditExecutable": true, "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Clip", "uninstallDisplayName": "Clip - Clipboard Manager", "deleteAppDataOnUninstall": false, "perMachine": false, "warningsAsErrors": false}, "extraResources": [{"from": "native/AutoHotkey.exe", "to": "AutoHotkey.exe"}, {"from": "native/SendPaste.exe", "to": "SendPaste.exe"}, {"from": "native/clipmsg.node", "to": "clipmsg.node"}], "mac": {"sign": false}, "linux": {}}}